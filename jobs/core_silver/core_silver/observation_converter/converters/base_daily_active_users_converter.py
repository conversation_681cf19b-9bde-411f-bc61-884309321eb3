import abc
from typing import Callable

import pandas as pd
import pandera as pa

from core_silver.observation_converter.converters.base_active_users_converter import (
    BaseActiveUsersConverter,
)
from data_sdk.reports.schema import DailyActiveUsersConvertedReport


class BaseDailyActiveUsersConverter(BaseActiveUsersConverter, abc.ABC):
    """
    Base class for daily active users converters.
    Handles daily-specific logic like date parsing and column mapping.
    """

    converted_report_cls = DailyActiveUsersConvertedReport

    # Daily-specific schema extends base schema with date field
    _schema = pa.DataFrameSchema({
        **BaseActiveUsersConverter._base_schema_fields,
        "date": pa.Column(pa.DateTime, coerce=True),
    })

    def _build_converted_dataframe(
        self, parsed_df: pd.DataFrame, manifest
    ) -> pd.DataFrame:
        """Build converted dataframe with daily-specific columns."""
        common_columns = self._get_common_columns(parsed_df, manifest)

        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            **common_columns,
            # Daily-specific columns
            date=parsed_df["date"].map(lambda x: x.strftime("%Y-%m-%d")),
            daily_active_users=parsed_df["count"],
        )
        return converted_df

    def _get_file_filter(self) -> Callable[[str], bool]:
        """Filter for daily active users files."""
        return lambda filename: "daily_" in filename

    def _get_parse_dates(self) -> bool:
        """Parse dates for daily data."""
        return True

    def _get_dtype_mapping(self) -> dict:
        return {
            **self._get_common_dtype_mapping(),
            "date": str,
        }
